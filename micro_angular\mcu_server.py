import os, sys, time, network, machine, ujson

# --- Mount SD and add /sd/lib ---
try:
    sd = machine.SDCard()
    os.mount(sd, "/sd")
    if "/sd/lib" not in sys.path:
        sys.path.insert(0, "/sd/lib")
    print("SD mounted.")
except Exception as e:
    print("No SD:", e)

from microdot.microdot import Microdot, Response
Response.default_content_type = 'application/json'

# --- LAN init (your style) ---
def net_up():
    lan = network.LAN()
    lan.active(True)
    timeout = 10
    while timeout > 0:
        ip = lan.ifconfig()[0]
        if ip != '0.0.0.0':
            break
        time.sleep(1)
        timeout -= 1
    return lan.ifconfig()

net_cfg = net_up()
print("LAN IP:", net_cfg[0])

# --- Hardware: 3 LEDs + 2 Buttons ---
LED1 = machine.Pin("P006", machine.Pin.OUT)
LED2 = machine.Pin("P007", machine.Pin.OUT)
LED3 = machine.Pin("P008", machine.Pin.OUT)

BTN1 = machine.Pin("P009", machine.Pin.IN, machine.Pin.PULL_UP)
BTN2 = machine.Pin("P010", machine.Pin.IN, machine.Pin.PULL_UP)

# --- Microdot app ---
app = Microdot()

@app.get('/api/status')
def status(req):
    data = {
        "leds": {
            "1": LED1.value(),
            "2": LED2.value(),
            "3": LED3.value(),
        },
        "buttons": {
            "1": BTN1.value(),  # 1 = not pressed (pull-up), 0 = pressed
            "2": BTN2.value(),
        }
    }
    return data, 200, {"Content-Type": "application/json"}

@app.post('/api/leds')
def set_led(req):
    try:
        body = req.json
        led = int(body.get("led", 0))
        val = 1 if body.get("value") else 0
    except Exception as e:
        return {"error": "bad request"}, 400

    if led == 1:
        LED1.value(val)
    elif led == 2:
        LED2.value(val)
    elif led == 3:
        LED3.value(val)
    else:
        return {"error": "invalid led"}, 400

    return {"ok": True, "led": led, "value": val}

# -------- Run --------
def main():
    print("Server running on http://%s/api/status" % net_cfg[0])
    app.run(host='0.0.0.0', port=80, debug=False)

if __name__ == '__main__':
    main()

